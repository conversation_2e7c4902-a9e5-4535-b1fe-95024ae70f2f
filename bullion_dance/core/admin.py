from django.contrib import admin
from django.utils import timezone
from datetime import date

from django.utils.safestring import mark_safe

from .models import (
    TransactionAgency,
    TransactionUser,
    XAUSaleOrder,
    XAUBuyBackOrder,
    XPTSaleOrder,
    XPTBuyBackOrder,
    XAUOffsetRelation,
    XPTOffsetRelation,
    DailyClosingPrice,
    DailyBill,
)


@admin.register(TransactionAgency)
class TransactionAgencyAdmin(admin.ModelAdmin):
    list_display = ['name']
    search_fields = ['name']


@admin.register(TransactionUser)
class TransactionUserAdmin(admin.ModelAdmin):
    list_display = [
        'name',
        'agency',
        'monthly_rebate_factor',
        'daily_deduction_factor',
        'xau_margin_factor',
        'xpt_margin_factor',
        'current_xau_holding',
        'current_xpt_holding',
        'current_overnight_fee',
        'current_holding_analysis',
    ]
    search_fields = ['name', 'agency__name']
    list_filter = ['agency']

    def current_xau_holding(self, obj):
        """显示当前黄金持有量"""
        weight = obj.get_xau_holding_weight(date.today(), date.today())
        return f"{weight:.3f} g"

    current_xau_holding.short_description = '当前黄金持有量'

    def current_xpt_holding(self, obj):
        """显示当前铂金持有量"""
        weight = obj.get_xpt_holding_weight(date.today(), date.today())
        return f"{weight:.3f} g"

    current_xpt_holding.short_description = '当前铂金持有量'

    def current_overnight_fee(self, obj):
        """显示当前过夜费"""
        fee = obj.calculate_overnight_fee(date.today(), date.today())
        return f"{fee:.2f} 元"

    current_overnight_fee.short_description = '当前过夜费'

    def current_holding_analysis(self, obj):
        """显示当前持仓分析"""
        today = date.today()
        xau_analysis = obj.get_xau_holding_analysis(today, today)
        xpt_analysis = obj.get_xpt_holding_analysis(today, today)

        html = "<div style='font-family: monospace; font-size: 11px;'>"
        html += "<strong>黄金:</strong> "
        html += f"未抵消{len(xau_analysis['sales']['unmatched_orders']) + len(xau_analysis['buybacks']['unmatched_orders'])}笔, "
        html += f"部分抵消{len(xau_analysis['sales']['partially_offset_orders']) + len(xau_analysis['buybacks']['partially_offset_orders'])}笔<br>"
        html += "<strong>铂金:</strong> "
        html += f"未抵消{len(xpt_analysis['sales']['unmatched_orders']) + len(xpt_analysis['buybacks']['unmatched_orders'])}笔, "
        html += f"部分抵消{len(xpt_analysis['sales']['partially_offset_orders']) + len(xpt_analysis['buybacks']['partially_offset_orders'])}笔"
        html += "</div>"

        return mark_safe(html)

    current_holding_analysis.short_description = '当前持仓分析'


class TransactionOrderAdmin(admin.ModelAdmin):
    def display_offset_status(self, obj):
        """显示抵消状态"""
        if obj.offset_weight > 0:
            if obj.offset_weight >= obj.weight:
                return "完全抵消"
            else:
                return "部分抵消"
        return "未抵消"

    display_offset_status.short_description = '抵消状态'

    def display_remaining_weight(self, obj):
        return f"{obj.remaining_weight:.3f}"

    display_remaining_weight.short_description = '剩余重量'

    def display_remaining_amount(self,obj):
        return f"{obj.remaining_amount:.3f}"

    display_remaining_amount.short_description = '剩余金额'

    list_display = [
        'trans_user',
        'trans_date',
        'locked_price',
        'weight',
        'trans_amount',
        'display_offset_status',
        'display_remaining_weight',
        'display_remaining_amount',
    ]
    fields = [
        'trans_user',
        'trans_date',
        'locked_price',
        'weight',
        'trans_amount',
        'display_offset_status',
        'display_remaining_weight',
        'display_remaining_amount',
    ]
    readonly_fields = [
        'trans_amount',
        'display_offset_status',
        'display_remaining_weight',
        'display_remaining_amount',
    ]
    search_fields = [
        'trans_user__name'
    ]
    list_filter = [
        'trans_date'
    ]


class XAUOffsetRelationInline(admin.TabularInline):
    model = XAUOffsetRelation
    fk_name = 'sale_order'
    extra = 0
    fields = ['buyback_order', 'offset_weight', 'created_at']
    readonly_fields = ['created_at']


@admin.register(XAUSaleOrder)
class XAUSaleOrderAdmin(TransactionOrderAdmin):
    list_display = TransactionOrderAdmin.list_display
    readonly_fields = TransactionOrderAdmin.readonly_fields
    inlines = [XAUOffsetRelationInline]


class XAUOffsetRelationInlineBuyback(admin.TabularInline):
    model = XAUOffsetRelation
    fk_name = 'buyback_order'
    extra = 0
    fields = ['sale_order', 'offset_weight', 'created_at']
    readonly_fields = ['created_at']


@admin.register(XAUBuyBackOrder)
class XAUBuyBackOrderAdmin(TransactionOrderAdmin):
    list_display = TransactionOrderAdmin.list_display
    readonly_fields = TransactionOrderAdmin.readonly_fields
    inlines = [XAUOffsetRelationInlineBuyback]


@admin.register(XAUOffsetRelation)
class XAUOffsetRelationAdmin(admin.ModelAdmin):
    list_display = [
        'sale_order',
        'buyback_order',
        'offset_weight',
        'display_sale_remaining',
        'display_buyback_remaining',
        'created_at'
    ]

    list_filter = [
        'created_at',
        'sale_order__trans_user__agency',
    ]

    search_fields = [
        'sale_order__trans_user__name',
        'buyback_order__trans_user__name',
        'sale_order__id',
        'buyback_order__id',
    ]

    readonly_fields = ['created_at']

    fields = [
        'sale_order',
        'buyback_order',
        'offset_weight',
        'created_at',
    ]

    def display_sale_remaining(self, obj):
        """显示销售订单剩余重量"""
        remaining = obj.sale_order.get_remaining_weight()
        return f"{remaining:.3f} g"

    display_sale_remaining.short_description = '销售订单剩余'

    def display_buyback_remaining(self, obj):
        """显示回购订单剩余重量"""
        remaining = obj.buyback_order.get_remaining_weight()
        return f"{remaining:.3f} g"

    display_buyback_remaining.short_description = '回购订单剩余'

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """优化外键字段的查询"""
        if db_field.name == "sale_order":
            kwargs["queryset"] = XAUSaleOrder.objects.select_related('trans_user')
        elif db_field.name == "buyback_order":
            kwargs["queryset"] = XAUBuyBackOrder.objects.select_related('trans_user')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


class XPTOffsetRelationInline(admin.TabularInline):
    model = XPTOffsetRelation
    fk_name = 'sale_order'
    extra = 0
    fields = ['buyback_order', 'offset_weight', 'created_at']
    readonly_fields = ['created_at']


@admin.register(XPTSaleOrder)
class XPTSaleOrderAdmin(TransactionOrderAdmin):
    list_display = TransactionOrderAdmin.list_display
    readonly_fields = TransactionOrderAdmin.readonly_fields
    inlines = [XPTOffsetRelationInline]


class XPTOffsetRelationInlineBuyback(admin.TabularInline):
    model = XPTOffsetRelation
    fk_name = 'buyback_order'
    extra = 0
    fields = ['sale_order', 'offset_weight', 'created_at']
    readonly_fields = ['created_at']


@admin.register(XPTBuyBackOrder)
class XPTBuyBackOrderAdmin(TransactionOrderAdmin):
    list_display = TransactionOrderAdmin.list_display
    readonly_fields = TransactionOrderAdmin.readonly_fields
    inlines = [XPTOffsetRelationInlineBuyback]


@admin.register(XPTOffsetRelation)
class XPTOffsetRelationAdmin(admin.ModelAdmin):
    list_display = [
        'sale_order',
        'buyback_order',
        'offset_weight',
        'display_sale_remaining',
        'display_buyback_remaining',
        'created_at'
    ]

    list_filter = [
        'created_at',
        'sale_order__trans_user__agency',
    ]

    search_fields = [
        'sale_order__trans_user__name',
        'buyback_order__trans_user__name',
        'sale_order__id',
        'buyback_order__id',
    ]

    readonly_fields = ['created_at']

    fields = [
        'sale_order',
        'buyback_order',
        'offset_weight',
        'created_at',
    ]

    def display_sale_remaining(self, obj):
        """显示销售订单剩余重量"""
        remaining = obj.sale_order.get_remaining_weight()
        return f"{remaining:.3f} g"

    display_sale_remaining.short_description = '销售订单剩余'

    def display_buyback_remaining(self, obj):
        """显示回购订单剩余重量"""
        remaining = obj.buyback_order.get_remaining_weight()
        return f"{remaining:.3f} g"

    display_buyback_remaining.short_description = '回购订单剩余'

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """优化外键字段的查询"""
        if db_field.name == "sale_order":
            kwargs["queryset"] = XPTSaleOrder.objects.select_related('trans_user')
        elif db_field.name == "buyback_order":
            kwargs["queryset"] = XPTBuyBackOrder.objects.select_related('trans_user')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


@admin.register(DailyClosingPrice)
class DailyClosingPriceAdmin(admin.ModelAdmin):
    list_display = [
        'price_date',
        'display_xau_external_price',
        'display_xau_internal_price',
        'display_xpt_external_price',
        'display_xpt_internal_price',
        # 'display_price_change',
        'created_at',
    ]

    list_filter = [
        'price_date',
        'created_at',
    ]

    search_fields = [
        'price_date',
    ]

    readonly_fields = [
        'created_at',
        'updated_at',
    ]

    fields = [
        'price_date',
        ('xau_external_closing_price', 'xau_internal_closing_price'),
        ('xpt_external_closing_price', 'xpt_internal_closing_price'),
        'created_at',
        'updated_at',
    ]

    date_hierarchy = 'price_date'

    def display_xau_external_price(self, obj):
        """显示黄金外部收盘价"""
        return f"¥{obj.xau_external_closing_price:.3f}/g"

    display_xau_external_price.short_description = '黄金外部价'

    def display_xau_internal_price(self, obj):
        """显示黄金内部收盘价"""
        return f"¥{obj.xau_internal_closing_price:.3f}/g"

    display_xau_internal_price.short_description = '黄金内部价'

    def display_xpt_external_price(self, obj):
        """显示铂金外部收盘价"""
        return f"¥{obj.xpt_external_closing_price:.3f}/g"

    display_xpt_external_price.short_description = '铂金外部价'

    def display_xpt_internal_price(self, obj):
        """显示铂金内部收盘价"""
        return f"¥{obj.xpt_internal_closing_price:.3f}/g"

    display_xpt_internal_price.short_description = '铂金内部价'


@admin.register(DailyBill)
class DailyBillAdmin(admin.ModelAdmin):
    list_display = [
        'transaction_user',
        'bill_start_date',
        'bill_end_date',
        'display_bill_period_days',
        'display_xau_holding_weight',
        'display_xpt_holding_weight',
        'display_total_overnight_fee',
        'display_unmatched_orders_summary',
        'created_at',
    ]

    list_filter = [
        'bill_start_date',
        'bill_end_date',
        'transaction_user__agency',
        'created_at',
    ]

    search_fields = [
        'transaction_user__name',
        'transaction_user__agency__name',
    ]

    readonly_fields = [
        'display_bill_period_days',
        'display_xau_holding_weight',
        'display_xpt_holding_weight',
        'display_xau_overnight_fee',
        'display_xpt_overnight_fee',
        'display_total_overnight_fee',
        'display_xau_orders_analysis',
        'display_xpt_orders_analysis',
        'display_unmatched_orders_summary',
        'display_fully_offset_orders_summary',
        'created_at',
        'updated_at',
    ]

    fields = [
        'transaction_user',
        'bill_start_date',
        'bill_end_date',
        ('display_xau_holding_weight', 'display_xpt_holding_weight'),
        ('display_xau_overnight_fee', 'display_xpt_overnight_fee'),
        'display_total_overnight_fee',
        'display_xau_orders_analysis',
        'display_xpt_orders_analysis',
        'display_unmatched_orders_summary',
        'display_fully_offset_orders_summary',
        'created_at',
        'updated_at',
    ]

    date_hierarchy = 'bill_end_date'

    def display_xau_holding_weight(self, obj):
        """显示黄金过夜费计算重量"""
        return f"{obj.xau_holding_weight:.3f} g"

    display_xau_holding_weight.short_description = '黄金计费重量'

    def display_xpt_holding_weight(self, obj):
        """显示铂金过夜费计算重量"""
        return f"{obj.xpt_holding_weight:.3f} g"

    display_xpt_holding_weight.short_description = '铂金计费重量'

    def display_xau_overnight_fee(self, obj):
        """显示黄金过夜费"""
        return f"¥{obj.xau_overnight_fee:.2f}"
    display_xau_overnight_fee.short_description = '黄金过夜费'

    def display_xpt_overnight_fee(self, obj):
        """显示铂金过夜费"""
        return f"¥{obj.xpt_overnight_fee:.2f}"
    display_xpt_overnight_fee.short_description = '铂金过夜费'

    def display_total_overnight_fee(self, obj):
        """显示总过夜费"""
        return f"¥{obj.total_overnight_fee:.2f}"
    display_total_overnight_fee.short_description = '总过夜费'

    def display_bill_period_days(self, obj):
        """显示账单周期天数"""
        return f"{obj.bill_period_days} 天"
    display_bill_period_days.short_description = '周期天数'

    def display_xau_orders_analysis(self, obj):
        """显示黄金订单分析详情"""
        analysis = obj.xau_holding_analysis
        sales = analysis['sales']
        buybacks = analysis['buybacks']

        html = "<div style='font-family: monospace; font-size: 12px;'>"
        html += "<strong>黄金订单分析:</strong><br>"
        html += f"销售订单 - 完全抵消: {len(sales['fully_offset_orders'])}笔, "
        html += f"部分抵消: {len(sales['partially_offset_orders'])}笔, "
        html += f"未抵消: {len(sales['unmatched_orders'])}笔<br>"
        html += f"回购订单 - 完全抵消: {len(buybacks['fully_offset_orders'])}笔, "
        html += f"部分抵消: {len(buybacks['partially_offset_orders'])}笔, "
        html += f"未抵消: {len(buybacks['unmatched_orders'])}笔<br>"
        html += f"<strong>未抵消总重量: {sales['total_unmatched_weight'] + buybacks['total_unmatched_weight']:.3f}g</strong>"
        html += "</div>"

        return mark_safe(html)

    display_xau_orders_analysis.short_description = '黄金订单分析'

    def display_xpt_orders_analysis(self, obj):
        """显示铂金订单分析详情"""
        analysis = obj.xpt_holding_analysis
        sales = analysis['sales']
        buybacks = analysis['buybacks']

        html = "<div style='font-family: monospace; font-size: 12px;'>"
        html += "<strong>铂金订单分析:</strong><br>"
        html += f"销售订单 - 完全抵消: {len(sales['fully_offset_orders'])}笔, "
        html += f"部分抵消: {len(sales['partially_offset_orders'])}笔, "
        html += f"未抵消: {len(sales['unmatched_orders'])}笔<br>"
        html += f"回购订单 - 完全抵消: {len(buybacks['fully_offset_orders'])}笔, "
        html += f"部分抵消: {len(buybacks['partially_offset_orders'])}笔, "
        html += f"未抵消: {len(buybacks['unmatched_orders'])}笔<br>"
        html += f"<strong>未抵消总重量: {sales['total_unmatched_weight'] + buybacks['total_unmatched_weight']:.3f}g</strong>"
        html += "</div>"

        return mark_safe(html)

    display_xpt_orders_analysis.short_description = '铂金订单分析'

    def display_unmatched_orders_summary(self, obj):
        """显示未完全抵消订单汇总"""
        xau_count = obj.xau_unmatched_orders_count
        xpt_count = obj.xpt_unmatched_orders_count
        total_count = obj.total_unmatched_orders_count

        if total_count == 0:
            return mark_safe("<span style='color: green;'>全部抵消</span>")
        else:
            return f"黄金:{xau_count}笔 铂金:{xpt_count}笔 (共{total_count}笔)"

    display_unmatched_orders_summary.short_description = '未完全抵消订单'

    def display_fully_offset_orders_summary(self, obj):
        """显示完全抵消订单汇总"""
        xau_count = obj.xau_fully_offset_orders_count
        xpt_count = obj.xpt_fully_offset_orders_count
        total_count = xau_count + xpt_count

        return f"黄金:{xau_count}笔 铂金:{xpt_count}笔 (共{total_count}笔)"

    display_fully_offset_orders_summary.short_description = '完全抵消订单'
