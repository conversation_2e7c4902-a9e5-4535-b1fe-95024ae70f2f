# Generated by Django 5.2.7 on 2025-11-05 01:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='dailybill',
            options={'ordering': ['-bill_end_date', 'transaction_user__name'], 'verbose_name': '账单', 'verbose_name_plural': '账单'},
        ),
        migrations.AlterUniqueTogether(
            name='dailybill',
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name='dailybill',
            name='bill_end_date',
            field=models.DateField(help_text='账单周期的结束日期（包含）', verbose_name='账单结束日期'),
        ),
        migrations.AlterField(
            model_name='dailybill',
            name='bill_start_date',
            field=models.DateField(help_text='账单周期的开始日期（包含）', verbose_name='账单开始日期'),
        ),
        migrations.AlterUniqueTogether(
            name='dailybill',
            unique_together={('transaction_user', 'bill_start_date', 'bill_end_date')},
        ),
    ]
