from decimal import Decimal

from django.db import models
from django.db.models import F, DecimalField, Sum
from django.core.exceptions import ValidationError


class TransactionAgency(models.Model):
    class Meta:
        verbose_name = '交易代理机构'
        verbose_name_plural = '交易代理机构'

    name = models.CharField(
        max_length=128,
        verbose_name='名称',
    )

    def __str__(self):
        return self.name


class TransactionUser(models.Model):
    class Meta:
        verbose_name = '交易用户'
        verbose_name_plural = '交易用户'

    name = models.CharField(
        max_length=128,
        verbose_name='名称',
    )

    agency = models.ForeignKey(
        to=TransactionAgency,
        on_delete=models.PROTECT,
        verbose_name='交易代理机构',
    )

    monthly_rebate_factor = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        verbose_name='月结返点系数',
    )

    daily_deduction_factor = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        verbose_name='每日账扣系数',
    )

    xau_margin_factor = models.PositiveIntegerField(
        verbose_name='黄金保证金系数',
        help_text='每 1kg 黄金需要缴纳的保证金'
    )

    xpt_margin_factor = models.PositiveIntegerField(
        verbose_name='铂金保证金系数',
        help_text='每 1kg 铂金需要缴纳的保证金'
    )

    xau_over_night_fee_factor = models.PositiveIntegerField(
        verbose_name='黄金过夜费系数',
        help_text='每 1kg 黄金 1 天所需要缴纳的过夜费'
    )

    xpt_over_night_fee_factor = models.PositiveIntegerField(
        verbose_name='铂金过夜费系数',
        help_text='每 1kg 铂金 1 天所需要缴纳的过夜费'
    )

    @property
    def xau_remaining_orders_values(self):
        xau_sale_orders = XAUSaleOrder.objects.filter(
            trans_user=self,
        )
        xau_buy_back_orders = XAUBuyBackOrder.objects.filter(
            trans_user=self,
        )
        xau_orders = ...
        res = {
            {
                'weight': xau_order.remaining_weight,
                'locked_price': xau_order.locked_price,
            } for xau_order in xau_orders
        }
        return

    def __str__(self):
        return self.name


class TransactionOrder(models.Model):
    OffsetRelationModel = None

    trans_user = models.ForeignKey(
        to=TransactionUser,
        on_delete=models.PROTECT,
        verbose_name='交易用户',
    )

    trans_date = models.DateField(
        verbose_name='交易日期',
    )

    locked_price = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='锁定价格',
    )

    weight = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='重量',
        help_text='以 g 为单位',
    )

    trans_amount = models.GeneratedField(
        expression=F('locked_price') * F('weight'),
        output_field=DecimalField(max_digits=18, decimal_places=3),
        db_persist=True,
        verbose_name='交易金额',
    )

    @property
    def offset_weight(self):
        """获取已被抵消的重量"""
        if not self.pk:  # 如果订单还没有保存，返回0
            return 0
        return self.OffsetRelationModel.objects.filter(
            sale_order=self
        ).aggregate(
            total=Sum('offset_weight')
        )['total'] or 0

    @property
    def remaining_weight(self):
        """获取剩余未抵消的重量"""
        if self.weight is not None and self.offset_weight is not None:
            return self.weight - self.offset_weight
        return 0

    @property
    def remaining_amount(self):
        return self.remaining_weight * self.locked_price

    def __str__(self):
        base_str = f'{self.trans_user.name} - {self.trans_date} - 销售 {self.weight}g {self.locked_price}'
        if self.offset_weight > 0:
            base_str += f' (已抵消 {self.offset_weight}g, 剩余 {self.remaining_weight}g)'
        return base_str


class XAUOffsetRelation(models.Model):
    """黄金抵消关系表 - 支持重量拆分"""

    class Meta:
        verbose_name = '黄金抵消关系'
        verbose_name_plural = '黄金抵消关系'
        ordering = ['-created_at']

    sale_order = models.ForeignKey(
        to='XAUSaleOrder',
        on_delete=models.CASCADE,
        verbose_name='销售订单',
        related_name='offset_relations'
    )

    buyback_order = models.ForeignKey(
        to='XAUBuyBackOrder',
        on_delete=models.CASCADE,
        verbose_name='回购订单',
        related_name='offset_relations'
    )

    offset_weight = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='抵消重量',
        help_text='以 g 为单位'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    def __str__(self):
        return f'销售#{self.sale_order.id} ↔ 回购#{self.buyback_order.id} ({self.offset_weight}g)'

    def clean(self):
        """验证抵消关系的有效性"""
        from django.core.exceptions import ValidationError

        if self.sale_order and self.buyback_order:
            # 检查用户是否一致
            if self.sale_order.trans_user != self.buyback_order.trans_user:
                raise ValidationError('抵消的销售订单和回购订单必须属于同一用户')

            # 检查抵消重量必须大于0
            if self.offset_weight <= 0:
                raise ValidationError('抵消重量必须大于0')

            # 只有当订单都已保存时才进行剩余重量验证
            if self.sale_order.pk and self.buyback_order.pk:
                # 检查抵消重量不能超过销售订单剩余重量
                sale_remaining = self.sale_order.get_remaining_weight()
                if hasattr(self, 'pk') and self.pk:
                    # 编辑现有记录时，需要加回当前记录的重量
                    current_offset = XAUOffsetRelation.objects.get(pk=self.pk).offset_weight
                    sale_remaining += current_offset

                if self.offset_weight > sale_remaining:
                    raise ValidationError(
                        f'抵消重量 ({self.offset_weight}g) 不能超过销售订单剩余重量 ({sale_remaining}g)'
                    )

                # 检查抵消重量不能超过回购订单剩余重量
                buyback_remaining = self.buyback_order.get_remaining_weight()
                if hasattr(self, 'pk') and self.pk:
                    # 编辑现有记录时，需要加回当前记录的重量
                    current_offset = XAUOffsetRelation.objects.get(pk=self.pk).offset_weight
                    buyback_remaining += current_offset

                if self.offset_weight > buyback_remaining:
                    raise ValidationError(
                        f'抵消重量 ({self.offset_weight}g) 不能超过回购订单剩余重量 ({buyback_remaining}g)'
                    )
            else:
                # 如果订单还没保存，只能检查基本的重量限制
                if self.sale_order.weight and self.offset_weight > self.sale_order.weight:
                    raise ValidationError(
                        f'抵消重量 ({self.offset_weight}g) 不能超过销售订单重量 ({self.sale_order.weight}g)'
                    )
                if self.buyback_order.weight and self.offset_weight > self.buyback_order.weight:
                    raise ValidationError(
                        f'抵消重量 ({self.offset_weight}g) 不能超过回购订单重量 ({self.buyback_order.weight}g)'
                    )


class XAUSaleOrder(TransactionOrder):
    class Meta:
        verbose_name = '黄金销售订单'
        verbose_name_plural = '黄金销售订单'

    OffsetRelationModel = XAUOffsetRelation


class XAUBuyBackOrder(TransactionOrder):
    class Meta:
        verbose_name = '黄金回购订单'
        verbose_name_plural = '黄金回购订单'

    OffsetRelationModel = XAUOffsetRelation


class XPTSaleOrder(TransactionOrder):
    class Meta:
        verbose_name = '铂金销售订单'
        verbose_name_plural = '铂金销售订单'

    def __str__(self):
        offset_weight = self.get_offset_weight()
        remaining_weight = self.weight - offset_weight
        base_str = f'{self.trans_user.name} - {self.trans_date} - 销售 {self.weight}g'
        if offset_weight > 0:
            base_str += f' (已抵消 {offset_weight}g, 剩余 {remaining_weight}g)'
        return base_str

    def get_offset_weight(self):
        """获取已被抵消的重量"""
        if not self.pk:  # 如果订单还没有保存，返回0
            return 0
        return XPTOffsetRelation.objects.filter(
            sale_order=self
        ).aggregate(
            total=Sum('offset_weight')
        )['total'] or 0

    def get_remaining_weight(self):
        """获取剩余未抵消的重量"""
        return self.weight - self.get_offset_weight()


class XPTBuyBackOrder(TransactionOrder):
    class Meta:
        verbose_name = '铂金回购订单'
        verbose_name_plural = '铂金回购订单'

    def __str__(self):
        offset_weight = self.get_offset_weight()
        remaining_weight = self.weight - offset_weight
        base_str = f'{self.trans_user.name} - {self.trans_date} - 回购 {self.weight}g'
        if offset_weight > 0:
            base_str += f' (已抵消 {offset_weight}g, 剩余 {remaining_weight}g)'
        return base_str

    def get_offset_weight(self):
        """获取已被抵消的重量"""
        if not self.pk:  # 如果订单还没有保存，返回0
            return 0
        return XPTOffsetRelation.objects.filter(
            buyback_order=self
        ).aggregate(
            total=Sum('offset_weight')
        )['total'] or 0

    def get_remaining_weight(self):
        """获取剩余未抵消的重量"""
        return self.weight - self.get_offset_weight()


class XPTOffsetRelation(models.Model):
    """铂金抵消关系表 - 支持重量拆分"""

    class Meta:
        verbose_name = '铂金抵消关系'
        verbose_name_plural = '铂金抵消关系'
        ordering = ['-created_at']

    sale_order = models.ForeignKey(
        XPTSaleOrder,
        on_delete=models.CASCADE,
        verbose_name='销售订单',
        related_name='offset_relations'
    )

    buyback_order = models.ForeignKey(
        XPTBuyBackOrder,
        on_delete=models.CASCADE,
        verbose_name='回购订单',
        related_name='offset_relations'
    )

    offset_weight = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='抵消重量',
        help_text='以 g 为单位'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    def __str__(self):
        return f'销售#{self.sale_order.id} ↔ 回购#{self.buyback_order.id} ({self.offset_weight}g)'

    def clean(self):
        """验证抵消关系的有效性"""
        from django.core.exceptions import ValidationError

        if self.sale_order and self.buyback_order:
            # 检查用户是否一致
            if self.sale_order.trans_user != self.buyback_order.trans_user:
                raise ValidationError('抵消的销售订单和回购订单必须属于同一用户')

            # 检查抵消重量必须大于0
            if self.offset_weight <= 0:
                raise ValidationError('抵消重量必须大于0')

            # 只有当订单都已保存时才进行剩余重量验证
            if self.sale_order.pk and self.buyback_order.pk:
                # 检查抵消重量不能超过销售订单剩余重量
                sale_remaining = self.sale_order.get_remaining_weight()
                if hasattr(self, 'pk') and self.pk:
                    # 编辑现有记录时，需要加回当前记录的重量
                    current_offset = XPTOffsetRelation.objects.get(pk=self.pk).offset_weight
                    sale_remaining += current_offset

                if self.offset_weight > sale_remaining:
                    raise ValidationError(
                        f'抵消重量 ({self.offset_weight}g) 不能超过销售订单剩余重量 ({sale_remaining}g)'
                    )

                # 检查抵消重量不能超过回购订单剩余重量
                buyback_remaining = self.buyback_order.get_remaining_weight()
                if hasattr(self, 'pk') and self.pk:
                    # 编辑现有记录时，需要加回当前记录的重量
                    current_offset = XPTOffsetRelation.objects.get(pk=self.pk).offset_weight
                    buyback_remaining += current_offset

                if self.offset_weight > buyback_remaining:
                    raise ValidationError(
                        f'抵消重量 ({self.offset_weight}g) 不能超过回购订单剩余重量 ({buyback_remaining}g)'
                    )
            else:
                # 如果订单还没保存，只能检查基本的重量限制
                if self.sale_order.weight and self.offset_weight > self.sale_order.weight:
                    raise ValidationError(
                        f'抵消重量 ({self.offset_weight}g) 不能超过销售订单重量 ({self.sale_order.weight}g)'
                    )
                if self.buyback_order.weight and self.offset_weight > self.buyback_order.weight:
                    raise ValidationError(
                        f'抵消重量 ({self.offset_weight}g) 不能超过回购订单重量 ({self.buyback_order.weight}g)'
                    )


class DailyClosingPrice(models.Model):
    """每日收盘价"""

    class Meta:
        verbose_name = '每日收盘价'
        verbose_name_plural = '每日收盘价'
        unique_together = ['price_date']
        ordering = ['-price_date']

    price_date = models.DateField(
        verbose_name='价格日期',
        help_text='收盘价对应的日期',
        unique=True
    )

    # 外部收盘价（市场价格）
    xau_external_closing_price = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='黄金外部收盘价',
        help_text='每克黄金的市场收盘价格'
    )

    xpt_external_closing_price = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='铂金外部收盘价',
        help_text='每克铂金的市场收盘价格'
    )

    # 内部收盘价（用于计算）
    xau_internal_closing_price = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='黄金内部收盘价',
        help_text='每克黄金的内部收盘价格，用于市值计算'
    )

    xpt_internal_closing_price = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='铂金内部收盘价',
        help_text='每克铂金的内部收盘价格，用于市值计算'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )

    def __str__(self):
        return f'{self.price_date} - 黄金内部: {self.xau_internal_closing_price}, 铂金内部: {self.xpt_internal_closing_price}'


class DailyBill(models.Model):
    class Meta:
        verbose_name = '账单'
        verbose_name_plural = '账单'
        unique_together = ['transaction_user', 'bill_start_date', 'bill_end_date']
        ordering = ['-bill_end_date', 'transaction_user__name']

    transaction_user = models.ForeignKey(
        to=TransactionUser,
        on_delete=models.PROTECT,
        verbose_name='交易用户',
    )

    bill_start_date = models.DateField(
        verbose_name='账单开始日期',
        help_text='账单周期的开始日期（包含）'
    )

    bill_end_date = models.DateField(
        verbose_name='账单结束日期',
        help_text='账单周期的结束日期（包含）'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )

    def __str__(self):
        return f'{self.transaction_user.name} - {self.bill_start_date} 至 {self.bill_end_date}'

    def clean(self):
        """验证账单日期"""
        if self.bill_start_date and self.bill_end_date:
            if self.bill_start_date > self.bill_end_date:
                raise ValidationError('账单开始日期不能晚于结束日期')

    @property
    def bill_period_days(self):
        """账单周期天数"""
        return (self.bill_end_date - self.bill_start_date).days

    @property
    def xau_holding_weight(self):
        """黄金过夜费计算重量（未抵消销售重量+未抵消回购重量）"""
        return self.transaction_user.get_xau_holding_weight(self.bill_start_date, self.bill_end_date)

    @property
    def xpt_holding_weight(self):
        """铂金过夜费计算重量（未抵消销售重量+未抵消回购重量）"""
        return self.transaction_user.get_xpt_holding_weight(self.bill_start_date, self.bill_end_date)

    @property
    def xau_overnight_fee(self):
        """黄金过夜费（从 transaction_user 计算）"""
        weight_kg = self.xau_holding_weight / Decimal(1000)
        return weight_kg * self.transaction_user.xau_over_night_fee_factor

    @property
    def xpt_overnight_fee(self):
        """铂金过夜费（从 transaction_user 计算）"""
        weight_kg = self.xpt_holding_weight / Decimal(1000)
        return weight_kg * self.transaction_user.xpt_over_night_fee_factor

    @property
    def total_overnight_fee(self):
        """总过夜费（从 transaction_user 计算）"""
        return self.xau_overnight_fee + self.xpt_overnight_fee

    @property
    def xau_holding_analysis(self):
        """黄金持仓分析详情"""
        return self.transaction_user.get_xau_holding_analysis(self.bill_start_date, self.bill_end_date)

    @property
    def xpt_holding_analysis(self):
        """铂金持仓分析详情"""
        return self.transaction_user.get_xpt_holding_analysis(self.bill_start_date, self.bill_end_date)

    @property
    def xau_unmatched_orders_count(self):
        """黄金未完全抵消订单数量（包括部分抵消和完全未抵消）"""
        analysis = self.xau_holding_analysis
        return (len(analysis['sales']['partially_offset_orders']) +
                len(analysis['sales']['unmatched_orders']) +
                len(analysis['buybacks']['partially_offset_orders']) +
                len(analysis['buybacks']['unmatched_orders']))

    @property
    def xpt_unmatched_orders_count(self):
        """铂金未完全抵消订单数量（包括部分抵消和完全未抵消）"""
        analysis = self.xpt_holding_analysis
        return (len(analysis['sales']['partially_offset_orders']) +
                len(analysis['sales']['unmatched_orders']) +
                len(analysis['buybacks']['partially_offset_orders']) +
                len(analysis['buybacks']['unmatched_orders']))

    @property
    def total_unmatched_orders_count(self):
        """总未完全抵消订单数量"""
        return self.xau_unmatched_orders_count + self.xpt_unmatched_orders_count

    @property
    def xau_fully_offset_orders_count(self):
        """黄金完全抵消订单数量"""
        analysis = self.xau_holding_analysis
        return (len(analysis['sales']['fully_offset_orders']) +
                len(analysis['buybacks']['fully_offset_orders']))

    @property
    def xpt_fully_offset_orders_count(self):
        """铂金完全抵消订单数量"""
        analysis = self.xpt_holding_analysis
        return (len(analysis['sales']['fully_offset_orders']) +
                len(analysis['buybacks']['fully_offset_orders']))
